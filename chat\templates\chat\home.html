{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EduChat - Tu Espacio de Trabajo de IA Unificado</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'chat/css/landing.css' %}">
</head>
<body>
    <!-- Header -->
    <header>
        <nav>
            <a href="{% url 'home' %}" class="logo">EduChat</a>
            <ul class="nav-links">
                <li><a href="#features">Características</a></li>
                <li><a href="#pricing">Precios</a></li>
                <li><a href="#blog">Blog</a></li>
                <li><a href="#about">Acerca de</a></li>
                <li><a href="#support">Soporte</a></li>
            </ul>
            <div class="nav-cta">
                {% if user.is_authenticated %}
                    <span class="welcome-user">Hola, {{ user.username }}</span>
                    <form action="{% url 'logout' %}" method="POST" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-ghost">Cerrar Sesión</button>
                    </form>
                    <a href="{% url 'chat' %}" class="btn btn-primary">Ir al Chat</a>
                {% else %}
                    <a href="{% url 'login' %}" class="btn btn-ghost">Iniciar Sesión</a>
                    <a href="{% url 'register' %}" class="btn btn-primary">Comenzar Gratis</a>
                {% endif %}
            </div>
        </nav>
    </header>

    <main>
    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>Libera el Poder de Cada LLM</h1>
            <p>Conecta, Crea y Aprende con Múltiples Modelos de IA, Notas y Agentes, Todo en Una Interfaz Intuitiva.</p>
            <div class="hero-cta">
                <a href="{% if user.is_authenticated %}{% url 'chat' %}{% else %}{% url 'register' %}{% endif %}" class="btn btn-primary">Comenzar Gratis</a>
                <a href="#features" class="btn btn-ghost">Explorar Características</a>
            </div>
        </div>
        <div class="hero-visual">
            <div class="mockup">
                <div class="chat-interface">
                    <div class="model-selector">
                        🤖 GPT-4 • Claude • Gemini
                    </div>
                    <div class="message user">¿Cómo puedo optimizar mi flujo de trabajo con IA?</div>
                    <div class="message ai">¡Puedo ayudarte a optimizar tu flujo de trabajo! Déjame analizar tus procesos actuales y sugerir mejoras impulsadas por IA...</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="section-header">
            <h2>Tu Compañero de IA Todo-en-Uno</h2>
        </div>
        <div class="feature-grid">
            <div class="feature animate-on-scroll">
                <div class="feature-content">
                    <h3>Conecta con Cada LLM, Sin Problemas</h3>
                    <p>Accede, compara y cambia entre modelos de lenguaje líderes como GPT-4, Claude y Gemini dentro de una sola interfaz optimizada. No más pestañas o cuentas múltiples.</p>
                </div>
                <div class="feature-visual">
                    <div class="feature-icon">🔗</div>
                </div>
            </div>

            <div class="feature animate-on-scroll">
                <div class="feature-content">
                    <h3>Captura Ideas, Organiza Conocimiento</h3>
                    <p>Transforma conversaciones en insights accionables. Nuestras herramientas integradas de toma de notas y creación de cuadernos te ayudan a guardar fragmentos, estructurar pensamientos y construir una base de conocimiento personal junto a tus interacciones de IA.</p>
                </div>
                <div class="feature-visual">
                    <div class="feature-icon">📝</div>
                </div>
            </div>

            <div class="feature animate-on-scroll">
                <div class="feature-content">
                    <h3>Aprende, Experimenta, Innova</h3>
                    <p>Aprovecha la IA para una comprensión más profunda con pruebas interactivas para solidificar conceptos y un poderoso sistema multi-agente para simular escenarios complejos y explorar capacidades avanzadas de IA.</p>
                </div>
                <div class="feature-visual">
                    <div class="feature-icon">🧠</div>
                </div>
            </div>

            <div class="feature animate-on-scroll">
                <div class="feature-content">
                    <h3>Diseño Intuitivo, Resultados Poderosos</h3>
                    <p>Experimenta una interfaz minimalista y libre de distracciones diseñada para la eficiencia. Enfócate en tus ideas, no en las herramientas, con respuestas ultrarrápidas y una experiencia de usuario fluida.</p>
                </div>
                <div class="feature-visual">
                    <div class="feature-icon">⚡</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="testimonials">
        <div class="testimonials-container">
            <h2>Lo Que Dicen Nuestros Usuarios</h2>
            <div class="testimonial-grid">
                <div class="testimonial animate-on-scroll">
                    <p>"Esta aplicación ha cambiado completamente cómo interactúo con los LLMs. ¡La toma de notas es revolucionaria!"</p>
                    <div class="testimonial-author">Sarah Chen, Gerente de Producto</div>
                </div>
                <div class="testimonial animate-on-scroll">
                    <p>"Finalmente, un lugar para todas mis necesidades de IA. El sistema multi-agente es increíblemente poderoso para investigación."</p>
                    <div class="testimonial-author">Dr. Michael Rodríguez, Investigador</div>
                </div>
                <div class="testimonial animate-on-scroll">
                    <p>"El cambio fluido entre modelos ha optimizado dramáticamente mi flujo de trabajo de creación de contenido."</p>
                    <div class="testimonial-author">Emma Thompson, Creadora de Contenido</div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="cta-content">
            <h2>Experimenta el Futuro de la Interacción con IA</h2>
            <p>Únete a miles de usuarios que están revolucionando su productividad y aprendizaje con EduChat.</p>
            <a href="{% if user.is_authenticated %}{% url 'chat' %}{% else %}{% url 'register' %}{% endif %}" class="btn btn-white">Comenzar Gratis Hoy</a>
        </div>
    </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <div class="logo" style="color: white; margin-bottom: 1rem;">EduChat</div>
                <p style="color: #9ca3af; margin-bottom: 1rem;">Tu Espacio de Trabajo de IA Unificado</p>
                <p style="color: #6b7280; font-size: 0.9rem;">© 2025 EduChat Inc. Todos los derechos reservados.</p>
            </div>
            <div class="footer-section">
                <h3>Producto</h3>
                <ul>
                    <li><a href="#features">Características</a></li>
                    <li><a href="#pricing">Precios</a></li>
                    <li><a href="#integrations">Integraciones</a></li>
                    <li><a href="#roadmap">Hoja de Ruta</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Recursos</h3>
                <ul>
                    <li><a href="#blog">Blog</a></li>
                    <li><a href="#documentation">Documentación</a></li>
                    <li><a href="#help">Centro de Ayuda</a></li>
                    <li><a href="#api">API</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Empresa</h3>
                <ul>
                    <li><a href="#about">Acerca de Nosotros</a></li>
                    <li><a href="#careers">Carreras</a></li>
                    <li><a href="#contact">Contacto</a></li>
                    <li><a href="#privacy">Política de Privacidad</a></li>
                    <li><a href="#terms">Términos de Servicio</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Header background change on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
